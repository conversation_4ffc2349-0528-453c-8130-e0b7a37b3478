{"name": "Reddit MVP Generator", "nodes": [{"parameters": {"formTitle": "Reddit MVP Generator", "formDescription": "Select a subreddit to generate MVP startup ideas from trending posts", "formFields": {"values": [{"fieldLabel": "Subreddit", "fieldType": "text", "requiredField": true, "fieldOptions": {"placeholder": "e.g., entrepreneur, startups, smallbusiness"}}]}, "options": {}}, "id": "form_trigger", "name": "Select a subreddit", "type": "n8n-nodes-base.formTrigger", "typeVersion": 2, "position": [200, 300], "webhookId": "reddit-mvp-form"}, {"parameters": {"authentication": "oAuth2", "select": "workbook", "workbook": {"__rl": true, "value": "your-workbook-id", "mode": "list"}, "worksheet": {"__rl": true, "value": "UsedPosts", "mode": "list"}, "operation": "readRows", "options": {}}, "id": "get_used_slugs", "name": "Get used reddit post slugs", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 2, "position": [400, 200], "credentials": {"microsoftExcelOAuth2Api": {"id": "excel_oauth2", "name": "Microsoft Excel account"}}}, {"parameters": {"fieldToAggregate": "slug", "options": {"keepOnlyUniqueValues": true}}, "id": "flatten_slugs", "name": "Flatten list of slugs", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [600, 200]}, {"parameters": {"resource": "post", "operation": "getAll", "subreddit": "={{ $('Select a subreddit').first().json.subreddit }}", "sort": "hot", "limit": 10}, "id": "get_trending_posts", "name": "Get Trending Posts", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [400, 400], "credentials": {"redditOAuth2Api": {"id": "reddit_oauth2", "name": "Reddit account"}}}, {"parameters": {"fieldsToAggregate": {"values": [{"fieldToAggregate": "data.subreddit"}, {"fieldToAggregate": "data.id"}]}, "options": {}}, "id": "extract_subreddits_ids", "name": "Extract subreddits and post ids", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [600, 400]}, {"parameters": {"jsCode": "const subreddits = $input.first().json.data?.subreddit || [];\nconst postIds = $input.first().json.data?.id || [];\n\nconst slugs = [];\nfor (let i = 0; i < postIds.length; i++) {\n  if (subreddits[i] && postIds[i]) {\n    slugs.push({\n      slug: `r/${subreddits[i]}_${postIds[i]}`,\n      subreddit: subreddits[i],\n      postId: postIds[i]\n    });\n  }\n}\n\nreturn slugs.map(item => ({ json: item }));"}, "id": "create_post_slugs", "name": "Create post slugs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 400]}, {"parameters": {"mode": "append", "join": "inner", "options": {}}, "id": "merge_used_new_slugs", "name": "Merge used post slugs with new post slugs", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [800, 300]}, {"parameters": {"jsCode": "const allItems = $input.all();\nconst allSlugs = [];\n\nfor (const item of allItems) {\n  if (item.json.slug) {\n    allSlugs.push(item.json.slug);\n  }\n}\n\nreturn [{ json: { slugs: allSlugs } }];"}, "id": "create_list_all_slugs", "name": "Create list of all post slugs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 300]}, {"parameters": {"fieldsToCompare": "slug", "options": {}}, "id": "remove_duplicate_slugs", "name": "Remove duplicate post slugs", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [1200, 300]}, {"parameters": {"fieldToSplitOut": "slugs", "options": {}}, "id": "split_post_slugs", "name": "Split out post slugs", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1400, 200]}, {"parameters": {"authentication": "oAuth2", "select": "workbook", "workbook": {"__rl": true, "value": "your-workbook-id", "mode": "list"}, "worksheet": {"__rl": true, "value": "UsedPosts", "mode": "list"}, "operation": "appendRow", "columns": {"mappingMode": "defineBelow", "values": {"slug": "={{ $json.slug }}"}}, "options": {}}, "id": "update_used_posts", "name": "Update \"Used Posts\" with new post slugs", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 2, "position": [1600, 200], "credentials": {"microsoftExcelOAuth2Api": {"id": "excel_oauth2", "name": "Microsoft Excel account"}}}, {"parameters": {"jsCode": "const subreddits = $('Extract subreddits and post ids').first().json.data?.subreddit || [];\nconst postIds = $('Extract subreddits and post ids').first().json.data?.id || [];\nconst postTitles = $('Get Trending Posts').all().map(item => item.json.data?.title || '');\nconst postTexts = $('Get Trending Posts').all().map(item => item.json.data?.selftext || '');\n\nconst posts = [];\nfor (let i = 0; i < postIds.length; i++) {\n  if (subreddits[i] && postIds[i]) {\n    posts.push({\n      subreddit: subreddits[i],\n      postId: postIds[i],\n      title: postTitles[i] || '',\n      text: postTexts[i] || '',\n      slug: `r/${subreddits[i]}_${postIds[i]}`\n    });\n  }\n}\n\nreturn posts.map(item => ({ json: item }));"}, "id": "create_list_posts", "name": "Create list of posts", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 400]}, {"parameters": {"mode": "append", "join": "inner", "options": {}}, "id": "merge_slugs_posts", "name": "Merge new post slugs with post data", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [1400, 350]}, {"parameters": {"jsCode": "const allItems = $input.all();\nconst usedSlugs = $('Flatten list of slugs').first().json.data || [];\n\nconst newPosts = allItems.filter(item => {\n  const slug = item.json.slug;\n  return !usedSlugs.includes(slug);\n});\n\nreturn newPosts;"}, "id": "filter_used_posts", "name": "Filter out used posts", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1600, 350]}, {"parameters": {"batchSize": 1, "options": {}}, "id": "loop_posts", "name": "Loop over posts", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1800, 350]}, {"parameters": {"resource": "comment", "operation": "getAll", "subreddit": "={{ $json.subreddit }}", "postId": "={{ $json.postId }}", "limit": 50}, "id": "get_comments", "name": "Get Comments", "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [2000, 250], "credentials": {"redditOAuth2Api": {"id": "reddit_oauth2", "name": "Reddit account"}}}, {"parameters": {"mode": "append", "join": "inner", "options": {}}, "id": "merge_post_comments", "name": "<PERSON><PERSON> post with comments", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [2200, 300]}, {"parameters": {"jsCode": "const postData = $input.first().json;\nconst comments = $input.all().filter(item => item.json.data?.body).map(item => item.json.data.body);\n\nconst flattenedData = {\n  post_title: postData.title || '',\n  post_text: postData.text || '',\n  post_subreddit: postData.subreddit || '',\n  comments: comments.slice(0, 10).join('\\n\\n'), // Limit to first 10 comments\n  slug: postData.slug\n};\n\nreturn [{ json: flattenedData }];"}, "id": "flatten_post_comments", "name": "<PERSON><PERSON> post and comments", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2400, 300]}, {"parameters": {"model": "openai/gpt-4o-mini", "options": {"temperature": 0.7, "maxTokens": 1000}, "messages": {"values": [{"role": "system", "content": "You are an expert startup advisor. Analyze Reddit posts and comments to generate viable MVP startup ideas. Focus on identifying real pain points and market opportunities."}, {"role": "user", "content": "Based on this Reddit post and comments, generate a structured MVP startup idea:\n\nPost Title: {{ $json.post_title }}\nPost Content: {{ $json.post_text }}\nSubreddit: {{ $json.post_subreddit }}\nComments: {{ $json.comments }}\n\nPlease provide a JSON response with the following structure:\n{\n  \"mvp_name\": \"Brief descriptive name\",\n  \"industry\": \"Primary industry category\",\n  \"pain_point\": \"Main problem being solved\",\n  \"startup_costs\": \"Estimated initial investment range\",\n  \"revenue_potential\": \"Potential revenue model and scale\",\n  \"target_audience\": \"Primary target market\",\n  \"solution_summary\": \"Brief solution description\"\n}"}]}}, "id": "openrouter_chat", "name": "OpenRouter <PERSON>", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [2600, 300], "credentials": {"openAiApi": {"id": "openrouter_api", "name": "OpenRouter API"}}}, {"parameters": {"schemaType": "object", "jsonSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"mvp_name\": {\n      \"type\": \"string\",\n      \"description\": \"Brief descriptive name for the MVP\"\n    },\n    \"industry\": {\n      \"type\": \"string\",\n      \"description\": \"Primary industry category\"\n    },\n    \"pain_point\": {\n      \"type\": \"string\",\n      \"description\": \"Main problem being solved\"\n    },\n    \"startup_costs\": {\n      \"type\": \"string\",\n      \"description\": \"Estimated initial investment range\"\n    },\n    \"revenue_potential\": {\n      \"type\": \"string\",\n      \"description\": \"Potential revenue model and scale\"\n    },\n    \"target_audience\": {\n      \"type\": \"string\",\n      \"description\": \"Primary target market\"\n    },\n    \"solution_summary\": {\n      \"type\": \"string\",\n      \"description\": \"Brief solution description\"\n    }\n  },\n  \"required\": [\"mvp_name\", \"industry\", \"pain_point\", \"startup_costs\", \"revenue_potential\"]\n}"}, "id": "structured_parser", "name": "Structured Output Parser", "type": "n8n-nodes-base.structuredOutputParser", "typeVersion": 1, "position": [2800, 300]}, {"parameters": {"chainType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model": {"model": "openai/gpt-4o-mini", "options": {"temperature": 0.7, "maxTokens": 1000}}, "prompt": {"messages": [{"role": "system", "content": "You are an expert startup advisor. Analyze Reddit posts and comments to generate viable MVP startup ideas."}, {"role": "user", "content": "Based on this Reddit post and comments, generate a structured MVP startup idea:\n\nPost: {{ $json.post_title }}\nContent: {{ $json.post_text }}\nSubreddit: {{ $json.post_subreddit }}\nComments: {{ $json.comments }}\n\n{format_instructions}"}]}, "outputParser": "={{ $('Structured Output Parser').first() }}"}, "id": "ai_mvp_generator", "name": "AI powered MVP Generator", "type": "n8n-nodes-base.chainLlm", "typeVersion": 1, "position": [3000, 300]}, {"parameters": {"fieldsToSplitOut": "mvp_name,industry,pain_point,startup_costs,revenue_potential,target_audience,solution_summary", "options": {}}, "id": "split_llm_output", "name": "Split llm output prep for spreadsheet", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [3200, 300]}, {"parameters": {"authentication": "oAuth2", "select": "workbook", "workbook": {"__rl": true, "value": "your-workbook-id", "mode": "list"}, "worksheet": {"__rl": true, "value": "MVPIdeas", "mode": "list"}, "operation": "appendRow", "columns": {"mappingMode": "defineBelow", "values": {"MVP": "={{ $json.mvp_name }}", "Industry": "={{ $json.industry }}", "Pain Point": "={{ $json.pain_point }}", "Startup Costs": "={{ $json.startup_costs }}", "Revenue Potential": "={{ $json.revenue_potential }}", "Target Audience": "={{ $json.target_audience }}", "Solution Summary": "={{ $json.solution_summary }}", "Source Subreddit": "={{ $('Flatten post and comments').first().json.post_subreddit }}", "Date Generated": "={{ $now }}"}}, "options": {}}, "id": "append_excel_row", "name": "Append new row to Excel sheet", "type": "n8n-nodes-base.microsoftExcel", "typeVersion": 2, "position": [3400, 300], "credentials": {"microsoftExcelOAuth2Api": {"id": "excel_oauth2", "name": "Microsoft Excel account"}}}], "connections": {"Select a subreddit": {"main": [[{"node": "Get used reddit post slugs", "type": "main", "index": 0}, {"node": "Get Trending Posts", "type": "main", "index": 0}]]}, "Get used reddit post slugs": {"main": [[{"node": "Flatten list of slugs", "type": "main", "index": 0}]]}, "Flatten list of slugs": {"main": [[{"node": "Merge used post slugs with new post slugs", "type": "main", "index": 0}]]}, "Get Trending Posts": {"main": [[{"node": "Extract subreddits and post ids", "type": "main", "index": 0}]]}, "Extract subreddits and post ids": {"main": [[{"node": "Create post slugs", "type": "main", "index": 0}, {"node": "Create list of posts", "type": "main", "index": 0}]]}, "Create post slugs": {"main": [[{"node": "Merge used post slugs with new post slugs", "type": "main", "index": 1}]]}, "Merge used post slugs with new post slugs": {"main": [[{"node": "Create list of all post slugs", "type": "main", "index": 0}]]}, "Create list of all post slugs": {"main": [[{"node": "Remove duplicate post slugs", "type": "main", "index": 0}]]}, "Remove duplicate post slugs": {"main": [[{"node": "Split out post slugs", "type": "main", "index": 0}, {"node": "Merge new post slugs with post data", "type": "main", "index": 0}]]}, "Split out post slugs": {"main": [[{"node": "Update \"Used Posts\" with new post slugs", "type": "main", "index": 0}]]}, "Create list of posts": {"main": [[{"node": "Merge new post slugs with post data", "type": "main", "index": 1}]]}, "Merge new post slugs with post data": {"main": [[{"node": "Filter out used posts", "type": "main", "index": 0}]]}, "Filter out used posts": {"main": [[{"node": "Loop over posts", "type": "main", "index": 0}]]}, "Loop over posts": {"main": [[{"node": "Get Comments", "type": "main", "index": 0}]]}, "Get Comments": {"main": [[{"node": "<PERSON><PERSON> post with comments", "type": "main", "index": 1}]]}, "Merge post with comments": {"main": [[{"node": "<PERSON><PERSON> post and comments", "type": "main", "index": 0}]]}, "Flatten post and comments": {"main": [[{"node": "AI powered MVP Generator", "type": "main", "index": 0}]]}, "AI powered MVP Generator": {"main": [[{"node": "Split llm output prep for spreadsheet", "type": "main", "index": 0}]]}, "Split llm output prep for spreadsheet": {"main": [[{"node": "Append new row to Excel sheet", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "startup-automation", "name": "startup-automation"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "reddit-mining", "name": "reddit-mining"}], "triggerCount": 0, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}